import { toTypedSchema } from '@vee-validate/zod'

const userQuerySchema = toTypedSchema(
  z.object({
    name: z.string().optional(),
    displayName: z.string().optional(),
  }),
)

const userCreateSchema = toTypedSchema(
  z.object({
    name: z.string().regex(/[a-z][a-z0-9]+/),
    displayName: z.string().min(1),
    avatar: z.string().default(''),
    password: z.string().min(8).regex(/[A-Z]\w+/i),
    isAdmin: z.boolean().default(false),
    perms: z.array(z.string()),
  }),
)

const userUpdateSchema = toTypedSchema(
  z.object({
    name: z.string().readonly(),
    avatar: z.string().default(''),
    displayName: z.string(),
    perms: z.array(z.string()).default([]),
    roles: z.array(z.string()).default([]),
    isAdmin: z.boolean().default(false),
  }),
)

export function useUserQueryForm() {
  const userQueryForm = useForm({
    validationSchema: userQuerySchema,
  })
  return userQueryForm
}

export function useUserCreateForm() {
  const form = useForm({
    validationSchema: userCreateSchema,
  })
  return form
}

export function useUserEditForm() {
  const form = useForm({
    validationSchema: userUpdateSchema,
  })
  return form
}
