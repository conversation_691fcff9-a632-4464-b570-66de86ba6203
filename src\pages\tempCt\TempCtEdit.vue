<script setup lang="ts">
import Button from 'primevue/button'
import Dialog from 'primevue/dialog'

import { tempCtApi } from '~/api/tempCt'
import { tempCtEditForm, workSectionOptions } from './schema'

const props = defineProps<{
  id?: string
}>()

const emits = defineEmits<{
  save: []
}>()
const loading = ref(false)
const open = defineModel<boolean>('open')

const { resetForm, setValues, handleSubmit } = tempCtEditForm()

const save = handleSubmit(async (values) => {
  if (props.id) {
    try {
      loading.value = true
      await tempCtApi.update(props.id, values)
      success('更新成功')
      emits('save')
      open.value = false
      resetForm()
    }
    finally {
      loading.value = false
    }
  }
})

async function onShow() {
  resetForm()
  if (props.id) {
    try {
      loading.value = true
      const data = await tempCtApi.get(props.id)
      setValues({ ...data, ct: Number(data.ct) })
    }
    finally {
      loading.value = false
    }
  }
}
</script>

<template>
  <Dialog v-model:visible="open" modal header="更新临时CT" @show="onShow">
    <form @submit="save">
      <FormLayout>
        <LSelect name="workSection" label="工段" :options="workSectionOptions" />
        <LInput name="model" label="平台" />
        <LInput name="line" label="产线" />
        <LInput name="partNumber" label="产品号" />
        <LInput name="ct" label="CT(s)" />
      </FormLayout>
      <div class="mt-8 flex justify-center gap-8">
        <Button type="submit" :loading="loading" fluid icon="pi pi-save" label="保存" />
        <Button severity="secondary" fluid icon="pi pi-times" label="取消" @click="open = false" />
      </div>
    </form>
    <template #footer />
  </Dialog>
</template>
