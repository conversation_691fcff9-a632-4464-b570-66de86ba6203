import type { HTTPError, KyRequest, KyResponse, NormalizedOptions } from 'ky'
import ky from 'ky'
import { router } from '~/router/index'
import { useUserStore } from '~/stores/user'

// todo optimize token refresh

interface ErrResponse {
  code: string
  msg: string
}

const tokenStorage = useToken()
const api = ky.extend({
  retry: 0,
  prefixUrl: import.meta.env.VITE_APP_BASE_API,
  timeout: import.meta.env.DEV ? 90000 : 9000,
  parseJson: parseISOString2Date,
  stringifyJson: stringifyDate2ISOString,
  hooks: {
    beforeRequest: [
      addAuth,
    ],
    afterResponse: [
      checkRefreshToken,
    ],
    beforeError: [
      getErrResponse,
      handleUnAuthorized,
      toastError,
    ],
  },
})

async function getErrResponse(error: HTTPError) {
  try {
    const res = await error.response.json<ErrResponse>()
    error.message = res.msg
    error.name = res.code
    return error
  }
  catch {
    return error
  }
}

function toastError(err: HTTPError) {
  errorToast({
    summary: err.name,
    detail: err.message,
    life: 3000,
  })
  return err
}

function addAuth(request: KyRequest) {
  if (request.credentials === 'same-origin') {
    if (tokenStorage.accessToken.value && !tokenStorage.accessExpire.value) {
      const token = tokenStorage.accessToken.value
      request.headers.set(import.meta.env.VITE_APP_AUTH_HEADER, `Bearer ${token}`)
    }
    else if (tokenStorage.accessExpire.value && tokenStorage.canRefresh.value) {
      const token = tokenStorage.refreshToken.value
      request.headers.set(import.meta.env.VITE_APP_AUTH_HEADER, `Bearer ${token}`)
      request.headers.set(import.meta.env.VITE_APP_TOKEN_REFRESH_HEADER, '1')
    }
  }
}

function parseISOString2Date(text: string) {
  return JSON.parse(text, (key: string, value: any) => {
    const isoDateFormat = /^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}(?:\.\d*)?(?:[-+]\d{2}:?\d{2}|Z)?$/
    if (typeof value === 'string' && isoDateFormat.test(value)) {
      return new Date(value)
    }
    return value
  })
}

// server will set new access token to a header, get it an save in localStorage
function checkRefreshToken(request: KyRequest, options: NormalizedOptions, response: KyResponse) {
  const accessToken = response.headers.get(import.meta.env.VITE_APP_TOKEN_REFRESH_HEADER)
  if (accessToken) {
    const tokenStorage = useToken()
    tokenStorage.setAccessToken(accessToken)
  }
}

function stringifyDate2ISOString(object: any) {
  return JSON.stringify(object, (key: string, value: any) => {
    if (value instanceof Date) {
      return value.toISOString()
    }
    return value
  })
}

function kyGet(url: string, param?: object) {
  const params = new URLSearchParams()
  if (param) {
    for (const entry of Object.entries(param)) {
      if (entry[1] instanceof Date) {
        params.set(entry[0], entry[1].toISOString())
      }
      else if (typeof entry[1] === 'object') {
        params.set(entry[0], stringifyDate2ISOString(entry[1]))
      }
      else {
        params.set(entry[0], entry[1])
      }
    }
    return api.get(url, {
      searchParams: params,
    })
  }
  return api.get(url)
}

function kyPost(url: string, data?: any, param?: object) {
  const params = new URLSearchParams()
  if (param) {
    for (const entry of Object.entries(param)) {
      if (entry[1] instanceof Date) {
        params.set(entry[0], entry[1].toISOString())
      }
      else if (typeof entry[1] === 'object') {
        params.set(entry[0], stringifyDate2ISOString(entry[1]))
      }
      else {
        params.set(entry[0], entry[1])
      }
    }
  }
  return api.post(url, {
    json: data,
    searchParams: params,
    headers: {
      'Content-Type': 'application/json',
    },
  })
}

function kyPut(url: string, body: any) {
  return api.put(url, {
    body,
  })
}

function kyDelete(url: string) {
  return api.delete(url)
}

function kyPostForm(url: string, data: FormData, param?: object) {
  const params = new URLSearchParams()
  if (param) {
    for (const entry of Object.entries(param)) {
      if (entry[1] instanceof Date) {
        params.set(entry[0], entry[1].toISOString())
      }
      else if (typeof entry[1] === 'object') {
        params.set(entry[0], stringifyDate2ISOString(entry[1]))
      }
      else {
        params.set(entry[0], entry[1])
      }
    }
  }
  return api.post(url, {
    body: data,
    searchParams: params,
  })
}

async function handleUnAuthorized(err: HTTPError) {
  if (err.response.status === 401) {
    const userStore = useUserStore()
    userStore.clear()
    router.push('/login')
  }
  return err
}

export { api as kyApi, kyDelete, kyGet, kyPost, kyPostForm, kyPut }
