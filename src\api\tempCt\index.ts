import type { CreateTempCt, TempCt, UpdateTempCt } from './types'
import { kyGet, kyPost } from '~/utils/request'

export const tempCtApi = {
  list: () => kyGet('tempCtMaintenance/list').json<TempCt[]>(),
  create: (creatTempCt: CreateTempCt) => kyPost('tempCtMaintenance/create', creatTempCt).json<CreateTempCt>(),
  update: (id: string, updateTempCt: UpdateTempCt) => kyPost(`tempCtMaintenance/update/${id}`, updateTempCt).json<UpdateTempCt>(),
  delete: (id: string) => kyPost(`tempCtMaintenance/delete/${id}`),
  get: (id: string) => kyGet(`tempCtMaintenance/${id}`).json<TempCt>(),
}
