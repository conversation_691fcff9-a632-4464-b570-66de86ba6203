<script setup lang="ts">
import { toTypedSchema } from '@vee-validate/zod'
import Button from 'primevue/button'

import { useForm } from 'vee-validate'
import { z } from 'zod'
import { userApi } from '~/api/common/user'
import { useUserStore } from '~/stores/user'
import { zext } from '~/utils/zodext'

const targetEnum = z.enum([
  '_blank',
  '_self',
  '_parent',
])

const targetOptions = targetEnum.options.map((o) => {
  return {
    label: o,
    value: o,
  }
})

const schema = toTypedSchema(z.object({
  path: z.string().startsWith('/'),
  title: z.string().min(1),
  target: targetEnum,
  file: zext.attachment().min(1).max(2),
  date: z.date(),
  redirect: z.boolean().default(false),
}))

const { resetForm, handleSubmit } = useForm({
  validationSchema: schema,
})

const { value } = useField<boolean>('redirect')

const save = handleSubmit((values) => {
  info(JSON.stringify(values))
})

const { user } = useUserStore()

function testRestPd() {
  if (user?.id) {
    userApi.resetPassword(user?.id, '123456')
  }
}
</script>

<template>
  <div>
    <form class="w-md p-4" @submit="save">
      <LInput name="path" label="路径" />
      <LSelect name="target" label="target" :options="targetOptions" />
      <LInput name="title" label="标题" />
      <LDatePicker name="date" label="日期" />

      <ToggleSwitch id="redirect" v-model="value" name="redirect" />

      <LabelFormItem name="ccc" label="文件">
        <SFileUpload name="file" :max="2" />
      </LabelFormItem>

      <Button class="mr-2 mt-4" type="submit">
        保存
      </Button>
      <Button outlined severity="second" @click="resetForm()">
        清空
      </Button>
      <Button @click="testRestPd">
        test reset
      </Button>
    </form>
  </div>
</template>
