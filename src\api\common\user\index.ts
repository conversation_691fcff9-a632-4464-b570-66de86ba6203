import type { Pageable, PageList } from '../types'
import type { UpdatePasswordParam, UserCreate, UserQuery, UserUpdate, UserView } from './type'
import { kyGet, kyPost } from '~/utils/request'

export const userApi = {
  resetPassword: (id: string, newPassword: string) => kyPost(`user/resetPassword/${id}`, null, { newPassword }),
  create: (param: UserCreate) => kyPost('user/create', param),
  update: (id: string, param: UserUpdate) => kyPost(`user/update/${id}`, param),
  get: (id: string) => kyGet(`user/${id}`).json<UserView>(),
  page: (param: Pageable<UserQuery>) => kyPost('user/page', param).json<PageList<UserView>>(),
  lock: (id: string) => kyPost(`user/lock/${id}`),
  updatePassword: (param: UpdatePasswordParam) => kyPost('user/updatePassword', param),
}
