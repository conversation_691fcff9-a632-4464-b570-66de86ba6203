<script setup lang="ts">
import type { DataTablePageEvent } from 'primevue'
import type { PageData } from '~/api/common/types'
import type { UserQuery, UserView } from '~/api/common/user/type'
import { useConfirm } from 'primevue'
import { userApi } from '~/api/common/user'
import PageContainer from '~/components/common/PageContainer.vue'
import { useUserQueryForm } from './schema'
import UserCreate from './UserCreate.vue'
import UserEdit from './UserEdit.vue'

const open = ref({
  create: false,
  edit: false,
})
const editId = ref<string>()

const pageData = reactive<PageData>({
  pageNumber: 0,
  pageSize: 10,
})

const total = ref(0)

const loading = ref<boolean>(false)

const data = ref<UserView[]>([])

const confirm = useConfirm()
const searchForm = useUserQueryForm()

const search = searchForm.handleSubmit(async (values: UserQuery) => {
  try {
    loading.value = true
    const res = await userApi.page({ searchParams: values, pageData })
    data.value = res.list
    total.value = res.total
  }
  finally {
    loading.value = false
  }
})

function page(e: DataTablePageEvent) {
  pageData.pageNumber = e.page
  pageData.pageSize = e.rows
  search()
}

function openEditPage(id: string) {
  editId.value = id
  open.value.edit = true
}

function openCreatePage() {
  open.value.create = true
}

function confirmLock(id: string, event: any) {
  confirm.require({
    group: 'delete',
    target: event.currentTarget,
    message: '确认锁定？',
    accept: async () => {
      await userApi.lock(id)
      success('锁定成功')
      data.value[data.value.findIndex(o => o.id === id)].locked = true
      // data.value = data.value.filter(o => o.id !== id)
    },
  })
}

onMounted(async () => {
  await search()
})
</script>

<template>
  <PageContainer>
    <SearchBox :loading="loading" @submit="search">
      <FInput name="name" label="用户名" />
      <FInput name="displayName" label="姓名" />
    </SearchBox>
    <div class="pl-4">
      <Button outlined icon="pi pi-plus" @click="openCreatePage" />
    </div>
    <DataTable class="mt-4 p-4" :value="data" lazy paginator data-key="id" :rows-per-page-options="[10, 20]" :rows="pageData.pageSize" :total-records="total" @page="page">
      <Column field="id" header="ID" />
      <Column field="name" header="用户名" />
      <Column field="displayName" header="姓名" />
      <Column header="是否管理员">
        <template #body="slotProps">
          {{ slotProps.data.isAdmin ? '是' : '否' }}
        </template>
      </Column>
      <Column field="locked" header="是否锁定">
        <template #body="slotProps">
          {{ slotProps.data.locked ? '是' : '否' }}
        </template>
      </Column>
      <Column header="操作">
        <template #body="slotProps">
          <div class="flex gap-4">
            <Button outlined icon="pi pi-pencil" @click="openEditPage(slotProps.data.id)" />
            <Button v-show="!slotProps.data.locked && !slotProps.data.isAdmin" outlined severity="danger" icon="pi pi-lock" @click="confirmLock(slotProps.data.id, $event)" />
          </div>
        </template>
      </Column>
      <template #empty>
        <TableEmpty />
      </template>
    </DataTable>
    <UserCreate v-model:open="open.create" @save="search" />
    <UserEdit :id="editId" v-model:open="open.edit" @save="search" />
  </PageContainer>
</template>
