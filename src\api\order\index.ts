import type { FileRef } from '../common/attachment/types'
import type { Pageable, PageList } from '../common/types'
import type { OrderSet } from './types'
import { kyGet, kyPost, kyPostForm } from '~/utils/request'

export const orderSetApi = {
  page: (param: Pageable<object>) => kyPost('order/page', param).json<PageList<OrderSet>>(),
  // upload: (uploadDate: string, file: File) => {
  upload: (file: File) => {
    const data = new FormData()
    // data.set('uploadDate', uploadDate)
    data.set('file', file)
    return kyPostForm('order/upload', data).json<FileRef>()
  },
  checkExist: () => kyGet('order/checkExist').json<boolean>(),
}
