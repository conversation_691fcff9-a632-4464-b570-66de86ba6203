<script setup lang="ts">
import type { MultiSelectProps } from 'primevue'
import { useField } from 'vee-validate'
import { useDictStore } from '~/stores/dict'

const props = defineProps<{
  name: string
  code: string
  multiSelectProps?: MultiSelectProps
}>()
const store = useDictStore()
const dict = computed(() => store.get(props.code))
const { value, errorMessage } = useField<string[]>(() => props.name)
</script>

<template>
  <MultiSelect v-model="value" :options="dict?.itemList" :input-id="props.name" option-label="label" option-value="value" :invalid="errorMessage ? true : false" fluid v-bind="multiSelectProps" />
  <ErrorMsg :error-message="errorMessage" />
</template>
