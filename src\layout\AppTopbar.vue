<script setup>
import { useRouter } from 'vue-router'
import { useLayoutStore } from '~/stores/layout'
import { useMenuStore } from '~/stores/menu'
import { useUserStore } from '~/stores/user'
import AppConfigurator from './AppConfigurator.vue'
import UserPop from './UserPop.vue'

const router = useRouter()

const { toggleDarkMode } = useLayoutStore()
const { onMenuToggle } = useMenuStore()
const { hasLogin } = useUserStore()

function toLoginPage() {
  router.push('/login')
}
</script>

<template>
  <div class="layout-topbar">
    <div class="layout-topbar-logo-container">
      <button class="layout-menu-button layout-topbar-action" @click="onMenuToggle">
        <i class="pi pi-bars" />
      </button>
      <router-link to="/" class="layout-topbar-logo">
        <span>线体负荷测算系统</span>
      </router-link>
    </div>

    <div class="layout-topbar-actions">
      <div class="layout-config-menu">
        <button type="button" class="layout-topbar-action" @click="toggleDarkMode">
          <i class="pi" :class="isDark ? 'pi-moon' : 'pi-sun'" />
        </button>
        <div class="relative">
          <button
            v-styleclass="{ selector: '@next', enterFromClass: 'hidden', enterActiveClass: 'animate-scale-in animate-duration-100', leaveToClass: 'hidden', leaveActiveClass: 'animate-fade-out animate-duration-100', hideOnOutsideClick: true }"
            type="button"
            class="layout-topbar-action layout-topbar-action-highlight"
          >
            <i class="pi pi-palette" />
          </button>
          <AppConfigurator />
        </div>
      </div>

      <button
        v-styleclass="{ selector: '@next', enterFromClass: 'hidden', enterActiveClass: 'animate-scale-in animate-duration-100', leaveToClass: 'hidden', leaveActiveClass: 'animate-fade-out animate-duration-100', hideOnOutsideClick: true }"
        class="layout-topbar-action layout-topbar-menu-button"
      >
        <i class="pi pi-ellipsis-v" />
      </button>

      <div class="layout-topbar-menu hidden lg:block">
        <div class="layout-topbar-menu-content">
          <button type="button" class="layout-topbar-action">
            <i class="pi pi-calendar" />
            <span class="ml-2">Calendar</span>
          </button>
          <button type="button" class="layout-topbar-action">
            <i class="pi pi-inbox" />
            <span class="ml-2"> Messages</span>
          </button>
          <UserPop v-if="hasLogin" />
          <button v-else type="button" class="layout-topbar-action" @click="toLoginPage">
            <i class="pi pi-sign-in" />
            <span class="ml-2">Login</span>
          </button>
          <!-- <button type="button" class="layout-topbar-action">
            <i class="pi pi-user" />
            <span>Profile</span>
          </button> -->
        </div>
      </div>
    </div>
  </div>
</template>
