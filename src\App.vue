<script setup lang="ts">
import ConfirmPopup from 'primevue/confirmpopup'
import Toast from 'primevue/toast'
import { useLayoutStore } from './stores/layout'

useLayoutStore().init()
</script>

<template>
  <Toast />
  <ConfirmPopup group="delete">
    <template #container="{ message, acceptCallback, rejectCallback }">
      <div class="rounded p-4">
        <div text-lg>
          <i class="pi pi-exclamation-circle text-lg" />
          {{ message.message }}
        </div>
        <div class="mt-4 flex items-center justify-evenly gap-2">
          <Button icon="pi pi-check" severity="danger" size="small" @click="acceptCallback" />
          <Button icon="pi pi-times" outlined severity="secondary" size="small" text @click="rejectCallback" />
        </div>
      </div>
    </template>
  </ConfirmPopup>
  <ConfirmDialog group="confirm">
    <template #container="{ message, acceptCallback, rejectCallback }">
      <div class="rounded-lg p-6 shadow-lg">
        <div class="mb-4 flex items-center gap-3 text-lg">
          <i class="pi pi-exclamation-circle text-yellow-500" />
          <span>{{ message.message }}</span>
        </div>
        <div class="flex items-center justify-end gap-3">
          <Button
            icon="pi pi-check"
            severity="success"
            size="small"
            label="确认"
            @click="acceptCallback"
          />
          <Button
            icon="pi pi-times"
            outlined
            severity="secondary"
            size="small"
            label="取消"
            @click="rejectCallback"
          />
        </div>
      </div>
    </template>
  </ConfirmDialog>
  <RouterView />
</template>
