export interface UserView {
  id: string
  name: string
  avatar: string
  displayName: string
  perms: string[]
  roles: string[]
  isAdmin: boolean
  locked: boolean
}

export interface UserQuery {
  name?: string
  displayName?: string
}

export interface UserCreate {
  name: string
  displayName: string
  avatar: string
  password: string
  isAdmin: boolean
}

export interface UserUpdate {
  avatar: string
  displayName: string
  perms: string[]
  roles: string[]
  isAdmin: boolean
}

export interface UpdatePasswordParam {
  oldPassword: string
  newPassword: string
}
