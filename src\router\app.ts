import type { RouteRecordRaw } from 'vue-router'

export default [
  {
    path: '/schedule/:uploadDate',
    component: () => import('~/pages/schedule/index.vue'),
    name: 'schedule',
    props: true,
    meta: {

    },
  },
  {
    path: '/orderset',
    component: () => import('~/pages/orderset/index.vue'),
    name: 'orderSet',
    meta: {

    },
  },
  {
    path: '/ct',
    name: 'ct',
    component: () => import('~/pages/ct/index.vue'),
    meta: {
      isPublic: true,
    },
  },

  {
    path: '/tempCt',
    name: 'tempCt',
    component: () => import('~/pages/tempCt/index.vue'),
    meta: {
      isPublic: true,
    },
  },
  {
    path: '/dashboard',
    name: 'dashboard',
    component: () => import('~/pages/dashboard/index.vue'),
    props: true,
  },
] satisfies RouteRecordRaw[]
