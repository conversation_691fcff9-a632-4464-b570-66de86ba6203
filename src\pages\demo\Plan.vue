<script setup lang="ts">
import { Column } from 'primevue'

interface Order {
  id: string
  productType: string
  yearMonth: string // Format: YYYY-MM (Order's due/target month)
  total: number
  left: number // Remaining amount to be planned
}

interface Ct {
  productType: string
  line: string
  cycleTime: number // seconds per unit
}

interface Plan {
  orderId: string
  line: string
  productType: string
  costTime: number
  amount: number // Time allocated for this order on this line, in seconds
  yearMonth: string // The month this plan is actually scheduled IN
}

interface LineAvailableProductTime {
  line: string
  time: number // Total available production time for the month, in seconds
  yearMonth: string // Format: YYYY-MM
}

const selectedLine = ref<LineAvailableProductTime>()
const showAssignDialog = ref<boolean>(false)
// --- Reactive Data ---
const orders = ref<Order[]>([
  { id: 'ORD001', productType: 'A', yearMonth: '2025-07', total: 1000, left: 1000 },
  { id: 'ORD002', productType: 'B', yearMonth: '2025-07', total: 500, left: 500 },
  { id: 'ORD003', productType: 'A', yearMonth: '2025-08', total: 1200, left: 1200 },
  { id: 'ORD004', productType: 'C', yearMonth: '2025-07', total: 700, left: 700 },
  { id: 'ORD005', productType: 'A', yearMonth: '2025-07', total: 600, left: 600 },
  { id: 'ORD006', productType: 'B', yearMonth: '2025-08', total: 900, left: 900 },
  { id: 'ORD007', productType: 'A', yearMonth: '2025-09', total: 800, left: 800 }, // Added for future month planning
])

const leftOrders = computed(() => orders.value.filter(o => o.left > 0))

const cts = ref<Ct[]>([
  { productType: 'A', line: 'Line1', cycleTime: 10 },
  { productType: 'A', line: 'Line2', cycleTime: 12 },
  { productType: 'B', line: 'Line1', cycleTime: 15 },
  { productType: 'B', line: 'Line2', cycleTime: 18 },
  { productType: 'C', line: 'Line3', cycleTime: 8 },
])

const lineAvailableProductTime = ref<LineAvailableProductTime[]>([
  { line: 'Line1', time: 7200, yearMonth: '2025-07' }, // 200 hours
  { line: 'Line2', time: 5400, yearMonth: '2025-07' }, // 150 hours
  { line: 'Line3', time: 6300, yearMonth: '2025-07' }, // 175 hours
  { line: 'Line1', time: 7200, yearMonth: '2025-08' },
  { line: 'Line2', time: 5400, yearMonth: '2025-08' },
  { line: 'Line3', time: 6300, yearMonth: '2025-08' },
  { line: 'Line1', time: 7200, yearMonth: '2025-09' }, // Added for future month planning
  { line: 'Line2', time: 5400, yearMonth: '2025-09' },
  { line: 'Line3', time: 0, yearMonth: '2025-09' },
])

const yearMonthsOptions = computed(() => lineAvailableProductTime.value.map(o => o.yearMonth).filter((v, i, a) => a.indexOf(v) === i).map(o => o))

const selectedMonth = ref<string>(yearMonthsOptions.value[0])

const plans = ref<Plan[]>([])

const selectedOrder = ref<Order | null>(null)

const yearMonthLines = computed(() => {
  return lineAvailableProductTime.value
    .filter(o => o.yearMonth === selectedMonth.value)
})

const lineOptions = computed(() => {
  return lineAvailableProductTime.value
    .filter(o => selectedOrder.value && o.yearMonth <= selectedOrder.value.yearMonth)
    .filter(o => cts.value.some(ct => ct.line === o.line && ct.productType === selectedOrder.value?.productType))
    .filter((o, i, a) => a.findIndex(x => x.line === o.line && x.yearMonth === o.yearMonth) === i)
    .sort((a, b) => a.yearMonth.localeCompare(b.yearMonth))
})

const assignAmount = ref<number>(0)

function getLoad(line: LineAvailableProductTime) {
  if (line.time <= 0) {
    return 1.00
  }
  const relatedPlans = plans.value.filter(p => p.line === line.line && p.yearMonth === line.yearMonth)
  const totalPlannedTime = relatedPlans.reduce((sum, p) => sum + p.costTime, 0)
  return Number((totalPlannedTime / line.time).toFixed(2))
}

function getLoadColor(line: LineAvailableProductTime) {
  const load = getLoad(line)
  if (load < 0.9)
    return 'text-success'
  if (load < 1)
    return 'text-warn'
  return 'text-error'
}

function getCt(productType: string, line: string): Ct | undefined {
  return cts.value.find(ct => ct.productType === productType && ct.line === line)
}

function onClickPlan(order: Order) {
  selectedOrder.value = order
  showAssignDialog.value = true
}

function assignPlan() {
  if (selectedLine.value && assignAmount.value > 0) {
    plans.value.push({
      orderId: selectedOrder.value!.id,
      line: selectedLine.value!.line,
      amount: assignAmount.value,
      costTime: getCt(selectedOrder.value!.productType, selectedLine.value!.line)!.cycleTime * assignAmount.value,
      productType: selectedOrder.value!.productType,
      yearMonth: selectedLine.value!.yearMonth,
    })
    selectedOrder.value!.left -= assignAmount.value
    assignAmount.value = 0
    selectedLine.value = undefined
    selectedOrder.value = null
    showAssignDialog.value = false
  }
}
</script>

<template>
  <div class="grid grid-cols-24 h-full w-full gap-4">
    <DataTable v-model:selection="selectedOrder" class="grid-col-span-12" :value="leftOrders" paginator :rows="10">
      <Column field="id" header="订单ID" />
      <Column field="productType" header="产品号" />
      <Column field="yearMonth" header="月份" />
      <Column field="total" header="总量" />
      <Column field="left" header="剩余量" />
      <Column header="操作">
        <template #body="slotProps">
          <Button
            label="分配"
            class="p-button-text"
            @click="onClickPlan(slotProps.data)"
          />
        </template>
      </Column>
    </DataTable>
    <div class="grid-col-span-12 bg-content-background p-4">
      <Select v-model="selectedMonth" name="yearMonth" :options="yearMonthsOptions" />
      <div class="mt-4 w-full">
        <div
          v-for="line in yearMonthLines"
          :key="line.line"
        >
          <div class="grid grid-cols-12 mb-4 items-center">
            <div>{{ line.line }}</div>
            <div class="grid-col-span-10 h-12 flex bg-surface">
              <div
                v-for="plan in plans.filter(p => p.line === line.line && p.yearMonth === selectedMonth)" :key="plan.orderId"
                class="h-12 flex items-center justify-center border bg-primary"
                :style="{ width: `${plan.costTime / line.time * 100}%` }"
              >
                <div class="flex flex-col items-center">
                  <span>{{ plan.orderId }}  {{ plan.costTime }} s</span>
                </div>
              </div>
            </div>
            <div class="flex items-center justify-center" :class=" getLoadColor(line) ">
              {{ (getLoad(line) * 100).toFixed(2) }}%
            </div>
          </div>
        </div>
      </div>
    </div>

    <Dialog v-model:visible="showAssignDialog" :modal="true" @click.prevent>
      <template #container>
        <div class="p-4">
          <h3 class="mb-4 text-lg font-semibold">
            分配计划
          </h3>
          <div v-if="selectedOrder">
            <div class="mb-2">
              订单ID: {{ selectedOrder.id }}
            </div>
            <div class="mb-2">
              产品号: {{ selectedOrder.productType }}
            </div>
            <div class="mb-2">
              剩余量: {{ selectedOrder.left }} units
            </div>
            <div class="mb-2 flex items-center gap-4">
              <label>分配量</label>
              <InputNumber v-model="assignAmount" name="assignAmount" label="分配量 (数量)" :min="1" :max="selectedOrder.left" />
            </div>

            <div>
              <div class="mb-2">
                选择产线
              </div>
              <div v-for=" line in lineOptions" :key="line.line" class="cursor-pointer p-2 hover:bg-highlight-background" :class="{ 'outline-primary outline outline-1': selectedLine?.line === line.line && selectedLine.yearMonth === line.yearMonth }" @click="selectedLine = line">
                <div class="grid grid-cols-12 items-center gap-2 p-2">
                  <div>{{ line.line }}</div>
                  <div>{{ line.yearMonth }}</div>
                  <div class="grid-col-span-9 h-12 flex bg-surface">
                    <div
                      v-for="plan in plans.filter(p => p.line === line.line && p.yearMonth === line.yearMonth)" :key="plan.orderId"
                      class="h-full flex items-center justify-center border border-gray-300 bg-primary"
                      :style="{ width: `${plan.costTime / line.time * 100}%` }"
                    >
                      <div class="flex flex-col items-center">
                        <span>{{ plan.orderId }}  {{ plan.costTime }} s</span>
                      </div>
                    </div>
                  </div>
                  <div class="flex items-center justify-center" :style="{ color: getLoadColor(line) }">
                    {{ getLoad(line) * 100 }}%
                  </div>
                </div>
              </div>
              <Button
                label="确认分配" class="mt-4" :disabled="!selectedLine || assignAmount <= 0 || assignAmount > selectedOrder.left"
                @click="assignPlan()"
              />
            </div>
          </div>
        </div>
      </template>
    </Dialog>
  </div>
</template>
