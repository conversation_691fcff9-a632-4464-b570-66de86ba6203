interface ImportMetaEnv {
  readonly VITE_APP_BASE_API: string
  readonly VITE_APP_TITLE: string
  readonly VITE_APP_BASE_URL: string
  readonly VITE_APP_ACCESS_STORE: string
  readonly VITE_APP_REFRESH_STORE: string
  readonly VITE_APP_ACCESS_EXPIRES_STORE: string
  readonly VITE_APP_REFRESH_EXPIRES_STORE: string
  readonly VITE_APP_AUTH_HEADER: string
  readonly VITE_APP_TOKEN_REFRESH_HEADER: string
}

declare module 'primevue/toasteventbus' {
  import type { EventBus } from 'primevue/utils'

  export default {} as ReturnType<typeof EventBus>
}
