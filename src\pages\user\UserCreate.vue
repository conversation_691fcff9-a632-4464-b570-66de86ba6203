<script setup lang="ts">
import Button from 'primevue/button'
import Dialog from 'primevue/dialog'

import { userApi } from '~/api/common/user'
import { useUserCreateForm } from './schema'

const emits = defineEmits<{
  save: []
}>()
const loading = ref(false)
const open = defineModel<boolean>('open')

const { resetForm, handleSubmit } = useUserCreateForm()
const save = handleSubmit(async (values) => {
  try {
    loading.value = true
    await userApi.create(values)
    success('创建成功')
    emits('save')
    open.value = false
    resetForm()
  }
  finally {
    loading.value = false
  }
})

function onShow() {
  resetForm()
}
</script>

<template>
  <Dialog v-model:visible="open" modal header="创建用户" @show="onShow">
    <form @submit="save">
      <FormLayout>
        <LInput name="name" label="用户名" />
        <LInput name="displayName" label="姓名" />
        <LPassword name="password" label="密码" />
        <LBoolRadioGroup name="isAdmin" label="是否管理员" />
        <LDictMultiSelect name="perms" code="SYS_PERM" label="权限" />
      </FormLayout>
      <div class="mt-8 flex justify-center gap-8">
        <Button type="submit" :loading="loading" fluid icon="pi pi-save" label="保存" />
        <Button severity="secondary" fluid icon="pi pi-times" label="取消" @click="open = false" />
      </div>
    </form>
    <template #footer />
  </Dialog>
</template>
