{"type": "module", "private": true, "packageManager": "pnpm@10.13.1", "scripts": {"build": "vite build", "clean": "rimraf node_modules", "dev": "vite --port 3333 --open", "lint": "eslint . --fix", "typecheck": "vue-tsc --noEmit", "preview": "vite preview", "test": "vitest", "up": "taze major -I", "prepare": "husky"}, "dependencies": {"@primevue/themes": "^4.2.5", "@types/d3-scale": "^4.0.9", "@types/d3-scale-chromatic": "^3.1.0", "@vee-validate/zod": "^4.15.1", "@vueuse/core": "^13.5.0", "casdoor-vue-sdk": "^1.6.0", "d3-scale": "^4.0.2", "d3-scale-chromatic": "^3.1.0", "decimal.js": "^10.6.0", "i18next": "^24.2.1", "ky": "^1.7.4", "pinia": "^3.0.3", "pinia-plugin-persistedstate": "^4.4.1", "primeicons": "^6.0.1", "primevue": "^4.2.5", "vee-validate": "^4.15.1", "vue": "^3.5.17", "vue-router": "^4.5.1", "zod": "^3.24.1", "zod-i18n-map": "^2.27.0"}, "devDependencies": {"@antfu/eslint-config": "^4.17.0", "@iconify-json/carbon": "^1.2.5", "@iconify-json/ic": "^1.2.2", "@iconify-json/ri": "^1.2.5", "@primevue/auto-import-resolver": "^4.2.5", "@types/node": "^22.12.0", "@unocss/eslint-config": "^66.3.3", "@unocss/eslint-plugin": "^66.3.3", "@unocss/preset-web-fonts": "^66.3.3", "@unocss/reset": "^66.3.3", "@vitejs/plugin-vue": "^6.0.0", "@vue/test-utils": "^2.4.6", "eslint": "^9.31.0", "eslint-plugin-format": "^1.0.1", "husky": "^9.1.7", "jsdom": "^24.1.3", "lint-staged": "^16.1.2", "pnpm": "^10.13.1", "taze": "^19.1.0", "typescript": "5.8.3", "unocss": "^66.3.3", "unplugin-auto-import": "^19.3.0", "unplugin-vue-components": "^28.8.0", "vite": "^7.0.5", "vitest": "^3.2.4", "vue-tsc": "3.0.3"}, "lint-staged": {"*": "eslint --fix"}}