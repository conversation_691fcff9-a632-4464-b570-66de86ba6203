<script setup lang="ts">
import { storeToRefs } from 'pinia'
import Avatar from 'primevue/avatar'
import { useRouter } from 'vue-router'
import { useUserStore } from '~/stores/user'

const { user } = storeToRefs(useUserStore())
const { logout, getAvatar } = useUserStore()
const router = useRouter()

function logoutAndToLoginPage() {
  logout()
  router.push('/login')
}

function openChangePassword() {
  router.push('/user/changePassword')
}
</script>

<template>
  <div class="flex flex-col gap-4">
    <div class="flex items-center gap-4">
      <Avatar :image="getAvatar()" shape="circle" size="large" />
      <div>{{ user?.displayName }}</div>
    </div>
    <div>
      <ul class="m-0 flex flex-col list-none p-0">
        <li class="flex cursor-pointer items-center gap-2 rounded px-2 py-3 hover:bg-content-hover-background" @click="openChangePassword">
          <div class="text-surface-500 dark:text-surface-400">
            更改密码
          </div>
        </li>
        <li class="flex cursor-pointer items-center gap-2 rounded px-2 py-3 hover:bg-content-hover-background" @click="logoutAndToLoginPage()">
          <div class="text-surface-500 dark:text-surface-400">
            登出
          </div>
        </li>
      </ul>
    </div>
  </div>
</template>
