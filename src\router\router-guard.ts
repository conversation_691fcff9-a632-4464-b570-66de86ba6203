import type { Router } from 'vue-router'
import { HTTPError } from 'ky'
import { authApi } from '~/api/common/auth'

import { useMenuStore } from '~/stores/menu'
import { useUserStore } from '~/stores/user'

export const LOGIN_PATH = '/login'

export function globalGuard(router: Router) {
  const { accessExpire, canRefresh, accessToken, refreshToken } = useToken()
  router.beforeEach(async (to, _) => {
    //  如果曾经登录过，且accessToken过期，但refreshToken未过期，则刷新token
    if (accessExpire.value && canRefresh.value && refreshToken.value) {
      try {
        await authApi.refresh()
      }
      catch {

      }
    }
    const { menu, fetchMenu } = useMenuStore()
    // 未登录
    if (!accessToken.value) {
      // 未登录，且访问的不是公开页面或登录页，跳转到登录页
      if (!to.meta.isPublic && LOGIN_PATH !== to.path) {
        return LOGIN_PATH
      }
      return true
    }
    // 无菜单，查询菜单
    if (menu.length === 0 && to.path !== LOGIN_PATH) {
      fetchMenu()
    }
    if (to.path === LOGIN_PATH) {
      return true
    }

    const userStore = useUserStore()
    // 无用户，获取用户信息
    if (!userStore.user && accessToken.value) {
      try {
        await userStore.fetchUser()
      }
      catch (ex) {
        if (ex instanceof HTTPError) {
          if (ex.response.status === 401) {
            return '/401'
          }
        }
      }
    }
    // 公开页面
    if (to.meta.isPublic) {
      return true
    }
    // 无访问权限
    const { hasAccess } = useAccess()
    if (hasAccess(to.meta.access)) {
      return true
    }
    else {
      return '/403'
    }
  })
}
