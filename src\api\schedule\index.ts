import type { Schedule } from './type'
import type { PeriodType } from '~/utils/time'
import { kyGet, kyPost } from '~/utils/request'

export const scheduleApi = {
  get: (workSection: string, uploadDate: string, periodType: PeriodType) => kyGet(`orderSchedule/${workSection}/${uploadDate}/${periodType}`).json<Schedule>(),
  save: (schedule: Schedule) => kyPost('orderSchedule', schedule).json<Schedule>(),
  board: () => kyGet('orderSchedule/board').json<Schedule[]>(),
}
