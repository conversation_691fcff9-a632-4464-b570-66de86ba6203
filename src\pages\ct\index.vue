<script setup lang="ts">
import type { Ct } from '~/api/ct/types'
import { FilterMatchMode } from '@primevue/core/api'
import { onMounted, ref } from 'vue'
import { ctApi } from '~/api/ct'
import PageContainer from '~/components/common/PageContainer.vue'
import { errorToast, successToast } from '~/composables/toast'

const loading = ref<boolean>(false)
const cts = ref<Ct[]>([])
const workSections = ref()
const lines = ref()
const boards = ref()
const models = ref()
const fileInputRef = ref<HTMLInputElement | null>()

async function search() {
  try {
    loading.value = true
    const res = await ctApi.list()
    cts.value = res
    lines.value = Array.from(new Set(res.map(item => item.line)))
    workSections.value = Array.from(new Set(res.map(item => item.workSection)))
    boards.value = Array.from(new Set(res.map(item => item.board)))
    models.value = Array.from(new Set(res.map(item => item.model)))
  }
  finally {
    loading.value = false
  }
}

async function handleFileUpload(event: Event) {
  const target = event.target as HTMLInputElement
  const file = target.files?.[0]
  if (!file)
    return

  try {
    loading.value = true
    await ctApi.upload(file)
    successToast({
      summary: '上传成功',
      detail: 'CT文件已成功上传',
    })
    await search()
    if (fileInputRef.value)
      fileInputRef.value.value = ''
  }
  catch (error: any) {
    const errorMsg = error.response?.data?.message || error.message || '未知错误'
    errorToast({
      summary: '上传失败',
      detail: errorMsg,
    })
    console.error('文件上传失败:', error)
  }
  finally {
    loading.value = false
  }
}

const filters = ref({
  workSection: { value: null, matchMode: FilterMatchMode.EQUALS },
  partNumber: { value: null, matchMode: FilterMatchMode.CONTAINS },
  line: { value: null, matchMode: FilterMatchMode.EQUALS },
  board: { value: null, matchMode: FilterMatchMode.EQUALS },
  model: { value: null, matchMode: FilterMatchMode.EQUALS },
})

function openCreatePage() {
  if (fileInputRef.value)
    fileInputRef.value.click()
}

onMounted(() => {
  search()
})
</script>

<template>
  <PageContainer>
    <DataTable v-model:filters="filters" class="mt-4 p-4" :value="cts" paginator data-key="id" :rows="10" filter-display="row">
      <template #header>
        <div class="h-3rem flex items-center justify-between pl-4">
          <div class="flex items-center gap-2">
            <Button
              :loading="loading" icon="pi pi-plus" label="上传CT" class="align-items-center flex"
              @click="openCreatePage"
            />
          </div>
          <input ref="fileInputRef" type="file" accept=".xlsx,.xls" class="hidden" @change="handleFileUpload">
        </div>
        <div class="flex justify-end">
          <IconField>
            <InputIcon>
              <i class="pi pi-search" />
            </InputIcon>
            <InputText v-model="filters.partNumber.value" placeholder="查找产品号" />
          </IconField>
        </div>
      </template>

      <Column field="workSection" header="工段">
        <template #body="{ data }">
          <Tag :value="data.workSection" :severity="data.workSection" />
        </template>
        <template #filter="{ filterModel, filterCallback }">
          <Select v-model="filterModel.value" :options="workSections" placeholder="选择工段" :show-clear="true" @change="filterCallback()">
            <template #option="slotProps">
              <Tag :value="slotProps.option" :severity="slotProps.option" />
            </template>
          </Select>
        </template>
      </Column>
      <Column field="model" header="平台">
        <template #body="{ data }">
          <Tag :value="data.model" :severity="data.model" />
        </template>
        <template #filter="{ filterModel, filterCallback }">
          <Select v-model="filterModel.value" :options="models" placeholder="选择平台" :show-clear="true" @change="filterCallback()">
            <template #option="slotProps">
              <Tag :value="slotProps.option" :severity="slotProps.option" />
            </template>
          </Select>
        </template>
      </Column>
      <Column field="line" header="产线">
        <template #body="{ data }">
          <Tag :value="data.line" :severity="data.line" />
        </template>
        <template #filter="{ filterModel, filterCallback }">
          <Select v-model="filterModel.value" :options="lines" placeholder="选择线体" :show-clear="true" @change="filterCallback()">
            <template #option="slotProps">
              <Tag :value="slotProps.option" :severity="slotProps.option" />
            </template>
          </Select>
        </template>
      </Column>
      <Column field="partNumber" header="产品号" />
      <Column field="board" header="版面">
        <template #body="{ data }">
          <Tag :value="data.board" :severity="data.board" />
        </template>
        <template #filter="{ filterModel, filterCallback }">
          <Select v-model="filterModel.value" :options="boards" placeholder="选择版面" :show-clear="true" @change="filterCallback()">
            <template #option="slotProps">
              <Tag :value="slotProps.option" :severity="slotProps.option" />
            </template>
          </Select>
        </template>
      </Column>
      <Column field="ct" header="CT(s)" />
    </DataTable>
  </PageContainer>
</template>
