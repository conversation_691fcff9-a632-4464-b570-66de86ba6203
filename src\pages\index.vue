<script setup lang="ts">
import Decimal from 'decimal.js'
import { onMounted } from 'vue'
import { useRouter } from 'vue-router'

const x = ref('1')
const y = ref('2')
const z = computed(() => Decimal.mul(x.value, y.value))

const router = useRouter()

onMounted(() => {
  router.push('/orderset')
})
</script>

<template>
  <div>
    <InputText v-model="x" />
    <InputText v-model="y" />
    <div>{{ z }}</div>
  </div>
</template>
