<script setup lang="ts">
import Button from 'primevue/button'
import Dialog from 'primevue/dialog'

import { userApi } from '~/api/common/user'
import { useUserEditForm } from './schema'

const props = defineProps<{
  id?: string
}>()

const emits = defineEmits<{
  save: []
}>()
const loading = ref(false)
const open = defineModel<boolean>('open')

const { resetForm, setValues, handleSubmit } = useUserEditForm()

const save = handleSubmit(async (values) => {
  if (props.id) {
    try {
      loading.value = true
      await userApi.update(props.id, values)
      success('更新成功')
      emits('save')
      open.value = false
      resetForm()
    }
    finally {
      loading.value = false
    }
  }
})

async function onShow() {
  resetForm()
  if (props.id) {
    try {
      loading.value = true
      const data = await userApi.get(props.id)
      setValues(data)
    }
    finally {
      loading.value = false
    }
  }
}
</script>

<template>
  <Dialog v-model:visible="open" modal header="更新用户" @show="onShow">
    <form @submit="save">
      <FormLayout>
        <LInput name="name" label="用户名" :input-text-props="{ disabled: true }" />
        <LInput name="displayName" label="姓名" />
        <LBoolRadioGroup name="isAdmin" label="是否管理员" />
        <LDictMultiSelect name="perms" code="SYS_PERM" label="权限" />
      </FormLayout>
      <div class="mt-8 flex justify-center gap-8">
        <Button type="submit" :loading="loading" fluid icon="pi pi-save" label="保存" />
        <Button severity="secondary" fluid icon="pi pi-times" label="取消" @click="open = false" />
      </div>
    </form>
    <template #footer />
  </Dialog>
</template>
