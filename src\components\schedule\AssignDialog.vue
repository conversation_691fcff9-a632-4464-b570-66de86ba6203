<script setup lang="ts">
import type { PopoverMethods } from 'primevue'
import type { Ct } from '~/api/ct/types'
// import type { linePeriodWorkDay } from '~/api/line/type'
import type { LinePeriodWorkDay, Remain, ScheduleItem } from '~/api/schedule/type'
import type { WorkSection } from '~/utils/time'
import { Card } from 'primevue'
import { getWorkTime } from '~/utils/time'

const props = defineProps<{
  linePeriodWorkDays: LinePeriodWorkDay[]
  scheduleItems: ScheduleItem[]
  remain: Remain | undefined
  cts: Ct[]
}>()

const emits = defineEmits<{
  removeScheduleItem: [item: ScheduleItem]
  confirm: [scheduleItem: ScheduleItem, remain: Remain]
}>()
const loading = ref<boolean>(false)
const workSection = inject<WorkSection>('workSection')
const show = defineModel<boolean>('show')

const activeRemain = ref<Remain | undefined>(props.remain)
const assignQuantity = ref<number>(0)
const optionalAvailabileTime = computed(() => {
  if (props.remain) {
    return props.linePeriodWorkDays.filter(o =>
    // 时间在选择的订单之前
      (o.period <= (props.remain ? props.remain.period : '')
      // 存在CT
        && props.cts.findIndex(p => p.line === o.line && p.partNumber === (props.remain ? props.remain.partNumber : '')) > -1
      ))
  }
  return []
})

const popRef = ref<PopoverMethods>()

const toggleScheduleItem = ref<ScheduleItem>()
const selectedLine = ref<LinePeriodWorkDay>()

const costTime = computed(() => {
  if (selectedLine.value && activeRemain.value) {
    const ct = findCt(selectedLine.value.line, activeRemain.value.partNumber)
    if (!ct) {
      warn('未找到ct')
      return Number.POSITIVE_INFINITY.toString()
    }
    return mul(assignQuantity.value.toString(), ct)
  }
  return Number.POSITIVE_INFINITY.toString()
})

function getLineScheduleItem(line: LinePeriodWorkDay) {
  return props.scheduleItems.filter(o => o.line === line.line && o.productionPeriod === line.period)
}

function getLoad(line: LinePeriodWorkDay) {
  const load = div(getLineScheduleItem(line).map(o => o.costTime).reduce((x, y) => plus(x, y), '0'), getWorkTime(line, workSection))
  return load.mul(100)
}

function getWidth(costTime: string, line: LinePeriodWorkDay) {
  return `${div(costTime, getWorkTime(line, workSection).toString()).mul(100)}%`
}

function getLoadClass(line: LinePeriodWorkDay) {
  const load = getLoad(line)

  if (load.gte(100)) {
    return 'text-error font-bold'
  }
  else if (load.gt(90)) {
    return 'text-warn'
  }
  else {
    return 'text-success'
  }
}

function getLineSelectedClass(line: LinePeriodWorkDay) {
  if (selectedLine.value === line) {
    return 'outline outline-1 outline-primary'
  }
  return ''
}

function openPopover(item: ScheduleItem, event: Event) {
  toggleScheduleItem.value = item
  popRef.value?.toggle(event)
}

function closePopover() {
  popRef.value?.hide()
}

function removeScheduleItem(item: ScheduleItem | undefined) {
  if (item) {
    emits('removeScheduleItem', item)
    popRef.value?.hide()
  }
}

function findCt(line: string, partNumber: string | undefined) {
  if (!partNumber) {
    return undefined
  }
  const ct = props.cts.find(o => o.line === line && o.partNumber === partNumber)
  if (ct) {
    return ct.ct
  }
  return undefined
}

function onConfirm() {
  if (!selectedLine.value) {
    warn('请选择分配的产线')
    return
  }
  if (activeRemain.value) {
    loading.value = true
    const ct = findCt(selectedLine.value.line, activeRemain.value.partNumber)
    if (ct) {
      activeRemain.value.remain -= assignQuantity.value

      const scheduleItem: ScheduleItem = {
        line: selectedLine.value.line,
        productName: activeRemain.value.product,
        partNumber: activeRemain.value.partNumber,
        model: activeRemain.value.model,
        orderPeriod: activeRemain.value.period,
        productionPeriod: selectedLine.value.period,
        quantity: assignQuantity.value,
        costTime: mul(ct, assignQuantity.value.toString()),
      }
      emits('confirm', scheduleItem, activeRemain.value)
      show.value = false
      loading.value = false
    }
    else {
      warn('未找到CT')
    }
  }
}

function onShow() {
  activeRemain.value = props.remain
  assignQuantity.value = props.remain?.remain || 0
  selectedLine.value = undefined
}
</script>

<template>
  <Dialog
    v-model:visible="show" header="排产" class="w-4xl" :modal="true"
    @show="onShow"
  >
    <div class="w-full flex flex-col gap-8 p-4">
      <div class="grid grid-cols-2 items-center gap-4">
        <div>产品号:</div><div> {{ remain?.partNumber }}</div>
        <div>订单周期： </div><div> {{ remain?.period }}</div>
        <div>待排量: </div><div> {{ remain?.remain }}</div>

        <div>分配量</div><div><InputNumber v-model="assignQuantity" name="assignQuantity" label="分配量 (数量)" :min="1" :max="remain?.remain" /></div>
        <div>耗时:</div><div>{{ formatSeconds(costTime) }}</div>
      </div>
      <div>
        <div>选择产线及时间</div>
        <div
          v-for="line in optionalAvailabileTime" :key="line.line + line.period"
          class="grid grid-cols-24 mb-1 w-full items-center gap-2"
          :class="getLineSelectedClass(line)"
          @click="selectedLine = line"
        >
          <div class="grid-col-span-2">
            {{ line.line }}
          </div>
          <div class="grid-col-span-3">
            {{ line.period }}
          </div>
          <div class="grid-col-span-3">
            {{ findCt(line.line, remain?.partNumber) }}s
          </div>
          <div class="grid-col-span-14 h-12 flex gap-2px bg-surface">
            <div

              v-for="scheduleItem in getLineScheduleItem(line)" :key="scheduleItem.line + scheduleItem.orderPeriod + scheduleItem.partNumber"
              class="h-full cursor-pointer bg-primary"

              :style="{ width: getWidth(scheduleItem.costTime, line) }"
              @mouseenter="openPopover(scheduleItem, $event)"
            />
          </div>
          <div class="grid-col-span-2" :class="getLoadClass(line)">
            {{ (getLoad(line)).toFixed(2) }}%
          </div>
        </div>
        <Popover ref="popRef" @mouseleave="closePopover()">
          <Card>
            <template #header>
              {{ toggleScheduleItem?.partNumber }}
            </template>
            <template #content>
              <div>排产数量:{{ toggleScheduleItem?.quantity }}</div>
              <div>
                消耗时间: {{ formatSeconds(toggleScheduleItem?.costTime) }}
              </div>
            </template>
            <template #footer>
              <Button class="w-full" severity="danger" icon="pi pi-trash" size="small" @click="removeScheduleItem(toggleScheduleItem)" />
            </template>
          </Card>
        </Popover>
      </div>
    </div>
    <template #footer>
      <Button :loading="loading" @click="onConfirm">
        确定
      </Button>
    </template>
  </Dialog>
</template>
