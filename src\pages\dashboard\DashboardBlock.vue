<script setup lang="ts">
import type { Schedule } from '~/api/schedule/type'
import { scaleSequential } from 'd3-scale'
import { interpolateOrRd } from 'd3-scale-chromatic'
import { groupBy } from '~/utils/lists'
import { WorkSection } from '~/utils/time'

// 映射到0-100的范围

const props = defineProps<{
  data: Schedule
}>() // 使用 scaleSequential 定义一个 viridis 颜色映射
// interpolateViridis 接收一个0到1的归一化值，并返回一个RGB颜色字符串
const viridisCmap = scaleSequential(interpolateOrRd).domain([90, 100])
const schedule = ref<Schedule>()
const linePeriods = computed(() => {
  if (schedule.value) {
    return groupBy(schedule.value.linePeriodWorkDays, o => o.line)
  }
  return []
})
const periods = computed(() => {
  if (schedule.value) {
    return Array.from(new Set(schedule.value.linePeriodWorkDays.map(o => o.period)))
  }
  return []
})

watch(() => props.data, (newValue) => {
  if (newValue) {
    schedule.value = newValue
    schedule.value.linePeriodWorkDays.sort((x, y) => x.period.localeCompare(y.period))
  }
}, { immediate: true })

function getLineLoad(line: string, period: string) {
  if (!schedule.value) {
    return
  }
  const scheduleItems = schedule.value?.detail.filter(o => o.line === line && o.productionPeriod === period)
  const linePeriodWorkDay = schedule.value?.linePeriodWorkDays.find(o => o.line === line && o.period === period)
  if (!linePeriodWorkDay) {
    return
  }
  const totalWorkTime = getWorkTime(linePeriodWorkDay, WorkSection.SMT)
  const totalCostTime = scheduleItems.map(o => o.costTime).reduce((x, y) => plus(x, y), '0')

  return div(totalCostTime, totalWorkTime || '0.000000001').mul(100).toFixed(2)
}

function getColorByValue(number: string): string {
  let value = Number.parseFloat(number)
  if (value < 90) {
    return '#00AA00'
  }
  if (value > 100) {
    value = 100
  }
  return viridisCmap(value)
}

function getLineLoadColor(line: string, period: string): string {
  return getColorByValue(getLineLoad(line, period) || '-1')
}
</script>

<template>
  <Panel :header="`${schedule?.workSection}  ${schedule?.timePeriod}`">
    <div class="w-full overflow-x-auto">
      <div class="grid mb-4 w-full gap-4" :style="{ gridTemplateColumns: `repeat(${periods.length + 1},  minmax(5rem, 1fr))` }">
        <div />
        <div v-for="period in periods" :key="period" class="flex items-center justify-center">
          {{ period }}
        </div>
        <template v-for="line in linePeriods" :key="line.key">
          <div class="flex items-center justify-center">
            {{ line.key }}
          </div>
          <div
            v-for="periodLoad in line.items" :key="periodLoad.period" class="h-12 min-w-20 flex items-center justify-center bg-primary"
            :style="{ background: getLineLoadColor(periodLoad.line, periodLoad.period) }"
          >
            {{ ` ${getLineLoad(periodLoad.line, periodLoad.period)}` }}
          </div>
        </template>
      </div>
    </div>
  </Panel>
</template>
