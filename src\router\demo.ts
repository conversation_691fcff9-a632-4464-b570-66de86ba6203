import type { RouteRecordRaw } from 'vue-router'

export default [

  {
    path: '/demo/form',
    name: 'demoForm',
    component: () => import('~/pages/demo/FormDemo.vue'),
    meta: {
      isPublic: true,
    },
  },
  {
    path: '/demo/table',
    name: 'demoTable',
    component: () => import('~/pages/demo/DemoTable.vue'),

    meta: {
      isPublic: true,
    },
  },
  {
    path: '/demo/plan',
    name: 'plan',
    component: () => import('~/pages/demo/Plan.vue'),
    meta: {
      isPublic: true,
    },
  },
] satisfies RouteRecordRaw[]
