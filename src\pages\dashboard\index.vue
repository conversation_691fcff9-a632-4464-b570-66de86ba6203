<script setup lang="ts">
import type { Schedule } from '~/api/schedule/type'
import { scheduleApi } from '~/api/schedule'
import DashboardBlock from './DashboardBlock.vue'

const schedules = ref<Schedule[]>([])

async function fetchBoard() {
  return scheduleApi.board().then((res) => {
    schedules.value = res.sort((x, y) => x.workSection.localeCompare(y.workSection))
  })
}
onMounted(async () => {
  await fetchBoard()
})
</script>

<template>
  <div class="grid grid-cols-2 w-full gap-4">
    <DashboardBlock
      v-for="schedule in schedules" :key="schedule.workSection + schedule.timePeriod"
      :data="schedule"
    />
  </div>
</template>
