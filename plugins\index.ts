import { PrimeVueResolver } from '@primevue/auto-import-resolver'
import Vue from '@vitejs/plugin-vue'
import UnoCSS from 'unocss/vite'
import AutoImport from 'unplugin-auto-import/vite'
import Components from 'unplugin-vue-components/vite'

export const plugins = [
  Vue(),

  // https://github.com/antfu/unplugin-auto-import
  AutoImport({
    imports: [
      'vue',
      '@vueuse/core',
      'vee-validate',
    ],
    dts: 'types/auto-imports.d.ts',
    dirs: [
      './src/composables',
      './src/utils',
    ],
    vueTemplate: true,
  }),

  // // https://github.com/antfu/vite-plugin-components
  Components({
    dirs: ['./src/components'],
    dts: 'types/components.d.ts',
    resolvers: [PrimeVueResolver()],
  }),

  // https://github.com/antfu/unocss
  // see uno.config.ts for config
  UnoCSS(),
]
