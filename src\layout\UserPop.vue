<script setup lang="ts">
import Popover from 'primevue/popover'
import UserPanel from './UserPanel.vue'

const userPanelRef = ref()
function toggleUserPanel(event: any) {
  userPanelRef.value.toggle(event)
}
</script>

<template>
  <div>
    <button type="button" class="layout-topbar-action" @click="toggleUserPanel">
      <i class="pi pi-user" />
      <span class="ml-2" />
    </button>

    <Popover ref="userPanelRef">
      <UserPanel />
    </Popover>
  </div>
</template>
