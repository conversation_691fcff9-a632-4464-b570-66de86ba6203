import type { Dict } from '~/api/common/dict/types'
import { defineStore } from 'pinia'
import { dictApi } from '~/api/common/dict'

export const useDictStore = defineStore('dict', () => {
  const dictMap = ref<Map<string, Dict>>(new Map<string, Dict>())
  const fetchFailMap = ref<Map<string, number>>(new Map<string, number>())
  const dictLockMap = ref<Map<string, boolean>>(new Map<string, boolean>())
  async function fetchDict(code: string) {
    const count = fetchFailMap.value.get(code)
    if (dictLockMap.value.get(code) || (count !== undefined && count >= 3)) {
      return
    }
    try {
      dictLockMap.value.set(code, true)
      const res = await dictApi.getByCode(code)
      dictMap.value.set(code, res)
    }
    catch {
      fetchFailMap.value.set(code, count ? count + 1 : 1)
    }
    finally {
      dictLockMap.value.set(code, false)
    }
  }

  function get(code: string) {
    if (!dictMap.value.has(code)) {
      fetchDict(code)
    }
    return dictMap.value.get(code)
  }

  function getLabel(dictCode: string, value: string) {
    if (!dictMap.value.has(dictCode)) {
      fetchDict(dictCode)
    }
    return dictMap.value.get(dictCode)?.itemList.find(o => o.value === value)?.label
  }

  async function refresh() {
    dictMap.value = new Map<string, Dict>()
  }

  return { get, getLabel, refresh }
})
