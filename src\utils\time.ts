import type { LinePeriodWorkDay } from '~/api/schedule/type'
import Decimal from 'decimal.js'

export function formatSeconds(secondsStr: string | undefined): string {
  if (secondsStr === undefined) {
    return 'NaN'
  }
  let totalSeconds = Decimal(secondsStr)
  if (totalSeconds.lessThan(0)) {
    throw new Error('Input cannot be negative.')
  }
  if (totalSeconds.eq(0)) {
    return '0s'
  }

  const hours = totalSeconds.divToInt(3600)
  totalSeconds = totalSeconds.mod(3600)

  const minutes = totalSeconds.divToInt(60)
  totalSeconds = totalSeconds.mod(60)

  let result = ''
  if (hours.gt(0)) {
    result += `${hours.toString()}h `
  }
  if (minutes.gt(0)) {
    result += `${minutes.toString()}m `
  }
  if (totalSeconds.gt(0)) {
    result += `${totalSeconds.toString()}s `
  }

  return result
}

const DayWorkTime = {
  SMT: 23.5 * 3600,
  DIP: 22.5 * 3600,
}

export enum WorkSection {
  SMT = 'SMT',
  DIP = 'DIP',
}

export enum PeriodType {
  MONTHLY = 'MONTHLY',
  WEEKLY = 'WEEKLY',
}

export function getWorkTime(linePeriodWorkDay: LinePeriodWorkDay, workSection: WorkSection | undefined) {
  if (workSection) {
    return Decimal.mul(linePeriodWorkDay.periodWorkDays, DayWorkTime[workSection])
  }
  else {
    return new Decimal(1e8)
  }
}
