<script setup lang="ts">
import Button from 'primevue/button'
import Dialog from 'primevue/dialog'

import { tempCtApi } from '~/api/tempCt'
import { tempCtCreateForm, workSectionOptions } from './schema'

const emits = defineEmits<{
  save: []
}>()
const loading = ref(false)
const open = defineModel<boolean>('open')

const { resetForm, handleSubmit } = tempCtCreateForm()
const save = handleSubmit(async (values) => {
  try {
    loading.value = true
    // ct转为字符串类型
    values.ct = String(values.ct)
    await tempCtApi.create(values)
    success('创建成功')
    emits('save')
    open.value = false
    resetForm()
  }
  finally {
    loading.value = false
  }
})

function onShow() {
  resetForm()
}
</script>

<template>
  <Dialog v-model:visible="open" modal header="新建临时CT" @show="onShow">
    <form @submit="save">
      <FormLayout>
        <LSelect name="workSection" label="工段" :options="workSectionOptions" />
        <LInput name="model" label="平台" />
        <LInput name="line" label="产线" />
        <LInput name="partNumber" label="产品号" />
        <LInputNumber name="ct" label="CT(s)" :input-number-props="{ maxFractionDigits: 5 }" />
      </FormLayout>
      <div class="mt-8 flex justify-center gap-8">
        <Button type="submit" :loading="loading" fluid icon="pi pi-save" label="保存" />
        <Button severity="secondary" fluid icon="pi pi-times" label="取消" @click="open = false" />
      </div>
    </form>
    <template #footer />
  </Dialog>
</template>
