<script setup lang="ts">
import type { TempCt } from '~/api/tempCt/types'
import { FilterMatchMode } from '@primevue/core/api'
import { useConfirm } from 'primevue'
import { onMounted, ref } from 'vue'
import { tempCtApi } from '~/api/tempCt'
import PageContainer from '~/components/common/PageContainer.vue'
import TempCtCreate from './TempCtCreate.vue'
import TempCtEdit from './TempCtEdit.vue'

const loading = ref<boolean>(false)
const cts = ref<TempCt[]>([])
const workSections = ref()
const lines = ref()
const models = ref()
const open = ref({
  create: false,
  edit: false,
})
const editId = ref<string>()
async function search() {
  try {
    loading.value = true
    const res = await tempCtApi.list()
    cts.value = res
    lines.value = Array.from(new Set(res.map(item => item.line)))
    workSections.value = Array.from(new Set(res.map(item => item.workSection)))
    models.value = Array.from(new Set(res.map(item => item.model)))
  }
  finally {
    loading.value = false
  }
}

const filters = ref({
  workSection: { value: null, matchMode: FilterMatchMode.EQUALS },
  partNumber: { value: null, matchMode: FilterMatchMode.CONTAINS },
  line: { value: null, matchMode: FilterMatchMode.EQUALS },
  board: { value: null, matchMode: FilterMatchMode.EQUALS },
  model: { value: null, matchMode: FilterMatchMode.EQUALS },
})

onMounted(() => {
  search()
})

function openCreatePage() {
  open.value.create = true
}
function openEditPage(id: string) {
  editId.value = id
  open.value.edit = true
}

const confirm = useConfirm()
function confirmDel(id: string, event: any) {
  confirm.require({
    group: 'delete',
    target: event.currentTarget,
    message: '确认删除？',
    accept: async () => {
      await tempCtApi.delete(id)
      success('删除成功')
      search()
    },
  })
}
</script>

<template>
  <PageContainer>
    <DataTable v-model:filters="filters" class="mt-4 p-4" :value="cts" paginator data-key="id" :rows="10" filter-display="row">
      <template #header>
        <div class="pl-4">
          <Button label="新增临时CT" icon="pi pi-plus" @click="openCreatePage" />
        </div>
        <div class="flex justify-end">
          <IconField>
            <InputIcon>
              <i class="pi pi-search" />
            </InputIcon>
            <InputText v-model="filters.partNumber.value" placeholder="查找产品号" />
          </IconField>
        </div>
      </template>

      <Column field="workSection" header="工段">
        <template #body="{ data }">
          <Tag :value="data.workSection" :severity="data.workSection" />
        </template>
        <template #filter="{ filterModel, filterCallback }">
          <Select v-model="filterModel.value" :options="workSections" placeholder="选择工段" :show-clear="true" @change="filterCallback()">
            <template #option="slotProps">
              <Tag :value="slotProps.option" :severity="slotProps.option" />
            </template>
          </Select>
        </template>
      </Column>
      <Column field="model" header="平台">
        <template #body="{ data }">
          <Tag :value="data.model" :severity="data.model" />
        </template>
        <template #filter="{ filterModel, filterCallback }">
          <Select v-model="filterModel.value" :options="models" placeholder="选择平台" :show-clear="true" @change="filterCallback()">
            <template #option="slotProps">
              <Tag :value="slotProps.option" :severity="slotProps.option" />
            </template>
          </Select>
        </template>
      </Column>
      <Column field="line" header="产线">
        <template #body="{ data }">
          <Tag :value="data.line" :severity="data.line" />
        </template>
        <template #filter="{ filterModel, filterCallback }">
          <Select v-model="filterModel.value" :options="lines" placeholder="选择线体" :show-clear="true" @change="filterCallback()">
            <template #option="slotProps">
              <Tag :value="slotProps.option" :severity="slotProps.option" />
            </template>
          </Select>
        </template>
      </Column>
      <Column field="partNumber" header="产品号" />
      <Column field="ct" header="CT(s)" />
      <Column header="操作">
        <template #body="slotProps">
          <div class="flex gap-4">
            <Button outlined icon="pi pi-pencil" @click="openEditPage(slotProps.data.id)" />
            <Button outlined icon="pi pi-trash" severity="danger" size="small" @click="confirmDel(slotProps.data.id, $event)" />
          </div>
        </template>
      </Column>
    </DataTable>
    <TempCtCreate v-model:open="open.create" @save="search" />
    <TempCtEdit :id="editId" v-model:open="open.edit" @save="search" />
    <p style="color: red;">
      注意：1、临时CT无法覆盖真实CT&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;2、上传真实CT时会自动删除临时CT中存在的同一线体下相同的产品数据！
    </p>
  </PageContainer>
</template>
