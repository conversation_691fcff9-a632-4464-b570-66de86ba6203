import i18next from 'i18next'
import { z } from 'zod'
import { zodI18nMap } from 'zod-i18n-map'
// Import your language translation files
import translation from 'zod-i18n-map/locales/zh-CN/zod.json'

function attachment() {
  return z.array(z.object({
    name: z.string(),
    fileKey: z.string(),
    size: z.number(),
  }))
}

function password() {
  return z.string()
    .min(8, '密码至少需要8个字符') // 密码最小长度
    .max(20, '密码最多不能超过20个字符') // 密码最大长度
    .refine((password) => {
    // 定义检查不同字符类型的正则表达式
      const hasUppercase = /[A-Z]/.test(password)
      const hasLowercase = /[a-z]/.test(password)
      const hasDigit = /\d/.test(password)
      // 特殊字符可以根据你的具体需求定义，这里是一个常见的集合
      const hasSpecialChar = /[!@#$%^&*()_+\-=[\]{};':"\\|,.<>/?~` ]/.test(password)

      let complexityScore = 0
      if (hasUppercase)
        complexityScore++
      if (hasLowercase)
        complexityScore++
      if (hasDigit)
        complexityScore++
      if (hasSpecialChar)
        complexityScore++

      // 检查是否至少满足2种类型
      return complexityScore >= 2
    }, '至少包含大小写字母、数字、符号中两种类型')
}

// lng and resources key depend on your locale.
i18next.init({
  lng: 'zh-CN',
  resources: {
    'zh-CN': { zod: translation },
  },
})
z.setErrorMap(zodI18nMap)

// export configured zod instance
export { z }

export const zext = {
  attachment,
  password,
}
