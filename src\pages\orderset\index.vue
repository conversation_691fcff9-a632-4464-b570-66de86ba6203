<script setup lang="ts">
import type { DataTablePageEvent } from 'primevue'
import type { PageData } from '~/api/common/types'
import type { OrderSet } from '~/api/order/types'
import { useConfirm } from 'primevue'

import { useRouter } from 'vue-router'
import { orderSetApi } from '~/api/order'
import PageContainer from '~/components/common/PageContainer.vue'
import { errorToast, successToast } from '~/composables/toast'

const router = useRouter()

const confirm = useConfirm()

const pageData = reactive<PageData>({
  pageNumber: 0,
  pageSize: 10,
})

const total = ref(0)

const loading = ref<boolean>(false)

const data = ref<OrderSet[]>([])

async function search() {
  try {
    loading.value = true
    const res = await orderSetApi.page({ searchParams: {}, pageData })
    data.value = res.list
    total.value = res.total
  }
  finally {
    loading.value = false
  }
}

function page(e: DataTablePageEvent) {
  pageData.pageNumber = e.page
  pageData.pageSize = e.rows
  search()
}

const fileInputRef = ref<HTMLInputElement>()

async function handleFileUpload(event: any) {
  const file = event.files[0]
  if (!file)
    return

  try {
    loading.value = true
    await orderSetApi.upload(file)
    successToast({
      summary: '上传成功',
      detail: '订单文件已成功上传',
    })
    await search()
    if (fileInputRef.value)
      fileInputRef.value.value = ''
  }
  catch (error: any) {
    const errorMsg = error.response?.data?.message || error.message || '未知错误'
    errorToast({
      summary: '上传失败',
      detail: errorMsg,
    })
    console.error('订单上传失败:', error)
  }
  finally {
    loading.value = false
  }
}

function checkTodayUpload() {
  return orderSetApi.checkExist()
}

async function openCreatePage(event: any) {
  const exists = await checkTodayUpload()
  if (exists) {
    confirm.require({
      group: 'confirm',
      target: event.currentTarget,
      message: '今天已经上传过订单，继续上传会覆盖之前的订单，是否继续？',
      accept: () => {
        if (fileInputRef.value) {
          fileInputRef.value.click()
        }
      },
    })
  }
  else {
    if (fileInputRef.value) {
      fileInputRef.value.click()
    }
  }
}

onMounted(() => {
  search()
})

function onClickPlan(data: OrderSet) {
  router.push(`/schedule/${data.uploadDate}`)
}
</script>

<template>
  <PageContainer>
    <div class="flex items-center gap-2 pl-4">
      <Button :loading="loading" size="large" icon="pi pi-save" label="上传订单" @click="openCreatePage($event)" />
      <input
        ref="fileInputRef"
        type="file"
        accept=".xlsx,.xls"
        class="hidden"
        @change="(e: Event) => {
          const target = e.target as HTMLInputElement
          if (target.files?.length)
            handleFileUpload({ files: [target.files[0]] })
        }"
      >
    </div>
    <DataTable class="mt-4 p-4" :value="data" paginator lazy data-key="id" :rows-per-page-options="[10, 20]" :rows="pageData.pageSize" :total-records="total" @page="page">
      <Column field="uploadDate" header="上传日期" />
      <Column field="creator" header="上传人" />
      <Column field="fileName" header="文件名" />
      <Column header="操作">
        <template #body="slotProps">
          <Button
            label="计划"
            class="p-button-text"
            @click="onClickPlan(slotProps.data)"
          />
        </template>
      </Column>
      <template #empty>
        <TableEmpty />
      </template>
    </DataTable>
  </PageContainer>
</template>
