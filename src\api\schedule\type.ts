export interface ScheduleItem {
  partNumber: string
  productName: string
  model: string
  line: string
  orderPeriod: string
  productionPeriod: string
  quantity: number
  costTime: string
}

export interface Remain {
  partNumber: string
  model: string
  period: string
  product: string
  remain: number
}

export interface Schedule {
  uploadDate: string
  status: boolean
  workSection: string
  fileId: string
  version: number
  timePeriod: string
  linePeriodWorkDays: LinePeriodWorkDay[]
  detail: ScheduleItem[]

  remains: Remain[]
}

export interface ScheduleMeta {
  uploadDate: string
  status: boolean
  workSection: string
  fileId: string
  version: number
  timePeriod: string
}

export interface LinePeriodWorkDay {
  line: string
  periodWorkDays: string
  period: string
}
