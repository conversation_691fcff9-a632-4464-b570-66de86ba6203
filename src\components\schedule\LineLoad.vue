<script setup lang="ts">
import type { PopoverMethods } from 'primevue'
import type { LinePeriodWorkDay, ScheduleItem } from '~/api/schedule/type'
import type { WorkSection } from '~/utils/time'
import { div, plus } from '~/utils/decimal'
import { getWorkTime } from '~/utils/time'

const props = defineProps<{
  linePeriodWorkDays: LinePeriodWorkDay[]
  scheduleItem: ScheduleItem[]
}>()

const emits = defineEmits<{
  removeScheduleItem: [item: ScheduleItem]
  modifyLinePeriodWorkDay: [line: LinePeriodWorkDay]
}>()

const { hasAccess } = useAccess()

const workSection = inject<WorkSection>('workSection')

const popRef = ref<PopoverMethods>()

const toggleScheduleItem = ref<ScheduleItem>()

const toggleLine = ref<string>()
const toggleWorkDay = ref<number>(0)
const togglePeriod = ref<string>()
const lineLoadPopRef = ref<PopoverMethods>()
function getLineScheduleItem(line: LinePeriodWorkDay) {
  return props.scheduleItem.filter(o => o.line === line.line && o.productionPeriod === line.period)
}

function getLoad(line: LinePeriodWorkDay) {
  const load = div(getLineScheduleItem(line).map(o => o.costTime).reduce((x, y) => plus(x, y), '0'), getWorkTime(line, workSection))
  return load.mul(100)
}

function getLoadClass(line: LinePeriodWorkDay) {
  const load = getLoad(line)

  if (load.gte(100)) {
    return 'text-error font-bold'
  }
  else if (load.gt(90)) {
    return 'text-warn'
  }
  else {
    return 'text-success'
  }
}

function openPopover(item: ScheduleItem, event: MouseEvent) {
  popRef.value?.hide()
  toggleScheduleItem.value = item
  nextTick(() => popRef.value?.show(event))

  // popRef.value?.alignOverlay()
}

function openLineLoadPopover(line: LinePeriodWorkDay, event: MouseEvent) {
  lineLoadPopRef.value?.hide()
  toggleLine.value = line.line
  toggleWorkDay.value = Number(line.periodWorkDays)
  togglePeriod.value = line.period
  nextTick(() => lineLoadPopRef.value?.show(event))
}

function closePopover() {
  popRef.value?.hide()
}

function modifyLinePeriodWorkDay() {
  if (toggleLine.value && togglePeriod.value) {
    emits('modifyLinePeriodWorkDay', {
      line: toggleLine.value,
      period: togglePeriod.value,
      periodWorkDays: toggleWorkDay.value.toString(),
    })
    lineLoadPopRef.value?.hide()
  }
}

function removeScheduleItem(item: ScheduleItem | undefined) {
  if (item) {
    emits('removeScheduleItem', item)
    popRef.value?.hide()
  }
}

function getWidth(costTime: string, line: LinePeriodWorkDay) {
  return `${div(costTime, getWorkTime(line, workSection).toString()).mul(100)}%`
}
</script>

<template>
  <div class="w-full flex flex-col gap-2 p-4">
    <div v-for="line in linePeriodWorkDays" :key="line.line + line.period" class="grid grid-cols-24 w-full items-center gap-2">
      <div class="grid-col-span-2">
        <Button outlined size="small" :label="line.line" :disabled="!hasAccess('workDayEdit')" @click="openLineLoadPopover(line, $event)" />
        <!-- {{ `${line.line}` }} -->
      </div>
      <div class="grid-col-span-20 h-12 flex gap-2px bg-surface">
        <div

          v-for="item in getLineScheduleItem(line)" :key="item.line + item.orderPeriod + item.partNumber"
          class="h-full cursor-pointer bg-primary hover:bg-primary-hover"
          :class="item.orderPeriod !== item.productionPeriod ? 'bg-yellow-300' : ''"
          :style="{ width: getWidth(item.costTime, line) }"
          @click="openPopover(item, $event)"
        />
      </div>
      <div class="grid-col-span-2" :class="getLoadClass(line)">
        {{ (getLoad(line)).toFixed(2) }}%
      </div>
    </div>
    <Popover ref="popRef" @mouseleave="closePopover()">
      <div>
        <div class="text-lg">
          {{ toggleScheduleItem?.partNumber }}
        </div>
        <div>
          <div>排产数量: {{ toggleScheduleItem?.quantity }}</div>
          <div>
            消耗时间: {{ formatSeconds(toggleScheduleItem?.costTime) }}
          </div>
          <div>订单周期: {{ toggleScheduleItem?.orderPeriod }}</div>
          <div>排产周期: {{ toggleScheduleItem?.productionPeriod }}</div>
        </div>
        <div>
          <Button class="mt-2 w-full" severity="danger" icon="pi pi-trash" size="small" @click="removeScheduleItem(toggleScheduleItem)" />
        </div>
      </div>
    </Popover>
    <Popover ref="lineLoadPopRef" @mouseleave="closePopover()">
      <div>
        <div class="text-lg">
          {{ toggleLine }} - {{ togglePeriod }}
        </div>
        <div>
          工作日: <InputNumber v-model="toggleWorkDay" :max="31" :min="1" :max-fraction-digits="1" />
        </div>
        <div>
          <Button class="mt-2 w-full" icon="pi pi-check" size="small" @click="modifyLinePeriodWorkDay()" />
        </div>
      </div>
    </popover>
  </div>
</template>
