import type { RouteRecordRaw } from 'vue-router'

export default [
  {
    path: '/admin/menu',
    name: 'menu',
    component: () => import('~/pages/menu/index.vue'),
    meta: {

    },
  },
  {
    path: '/admin/dict',
    name: 'dict',
    component: () => import('~/pages/dict/index.vue'),
    meta: {

    },
  },
  {
    path: '/admin/user',
    name: 'user',
    component: () => import('~/pages/user/index.vue'),
  },
] satisfies RouteRecordRaw[]
