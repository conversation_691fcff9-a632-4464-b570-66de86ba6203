import { toTypedSchema } from '@vee-validate/zod'

const tempCtCreateSchema = toTypedSchema(
  z.object({
    workSection: z.string().min(1),
    line: z.string().min(1),
    partNumber: z.string().min(1),
    model: z.string().min(1),
    ct: z.number().min(0.01).transform(value => value.toString()),
  }),
)

const tempCtUpdateSchema = toTypedSchema(
  z.object({
    id: z.string(),
    workSection: z.string().min(1),
    line: z.string().min(1),
    partNumber: z.string().min(1),
    model: z.string().min(1),
    ct: z.number().min(0.01).transform(value => value.toString()),
  }),
)

export const workSectionOptions = [
  {
    label: 'SMT',
    value: 'SMT',
  },
  {
    label: 'DIP',
    value: 'DIP',
  },
]

export function tempCtCreateForm() {
  const form = useForm({
    validationSchema: tempCtCreateSchema,
  })
  return form
}

export function tempCtEditForm() {
  const form = useForm({
    validationSchema: tempCtUpdateSchema,
  })
  return form
}
