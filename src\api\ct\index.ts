import type { Ct } from './types'
import { kyGet, kyPostForm } from '~/utils/request'

export const ctApi = {
  list: () => kyGet('ctMaintenance/list').json<Ct[]>(),
  get: (workSection: string, uploadDate: string) => kyGet(`ctMaintenance/${workSection}/${uploadDate}`).json<Ct[]>(),
  upload: (excel: File) => {
    const data = new FormData()
    data.set('file', excel)
    return kyPostForm('ctMaintenance/upload', data).json()
  },
}
