import type { RouteRecordRaw } from 'vue-router'
import { createRouter, createWebHistory } from 'vue-router'

import adminRoute from './admin'
import appRouter from './app'
import commonRoute from './common'
import demoRoute from './demo'
import { globalGuard } from './router-guard'
import userRoute from './user'

function buildRoute(routes: RouteRecordRaw[][]) {
  const result = routes.flatMap(o => o)
  return result
}

const routes = buildRoute([commonRoute, adminRoute, demoRoute, userRoute, appRouter])
const layoutRoutes = [
  {
    path: '/',
    name: 'layout',
    component: () => import('~/layout/AppLayout.vue'),
    meta: {

    },
    children: routes.filter(o => !o.meta?.noLayout),
  },
  ...routes.filter(o => o.meta?.noLayout),
] satisfies RouteRecordRaw[]
const router = createRouter({
  routes: layoutRoutes,
  history: createWebHistory(import.meta.env.BASE_URL),
})

globalGuard(router)
export { router }
