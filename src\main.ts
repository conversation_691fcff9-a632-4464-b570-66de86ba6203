import Aura from '@primevue/themes/aura'
import Casdoor from 'casdoor-vue-sdk'
import { createPinia } from 'pinia'
import piniaPluginPersistedstate from 'pinia-plugin-persistedstate'
import PrimeVue from 'primevue/config'
import ConfirmationService from 'primevue/confirmationservice'
import Ripple from 'primevue/ripple'
import StyleClass from 'primevue/styleclass'
import ToastService from 'primevue/toastservice'
import { createApp } from 'vue'
import { router } from '~/router'
import App from './App.vue'
import '~/assets/styles/main.css'
import 'uno.css'

const config = {
  serverUrl: 'http://*************:8000',
  clientId: '2c1306cac7692e637120',
  organizationName: 'hongjing_internal',
  appName: 'platform',
  redirectPath: '/login',
}

const pinia = createPinia()
pinia.use(piniaPluginPersistedstate)
const app = createApp(App)

app.use(PrimeVue, {
  theme: {
    ripper: true,
    preset: Aura,
    options: {
      darkModeSelector: '.dark',
      cssLayer: {
        name: 'primevue',
      },
    },
  },
})
app.use(ToastService)
app.use(ConfirmationService)
app.directive('styleclass', StyleClass)
app.directive('ripple', Ripple)
app.use(Casdoor, config)
app.use(pinia)
app.use(router)
app.config.globalProperties.$formatSeconds = formatSeconds

app.mount('#app')
