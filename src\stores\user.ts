import { defineStore } from 'pinia'
import { authApi } from '~/api/common/auth'
import DefaultAvatar from '~/assets/logo.svg'

export interface User {
  id: string
  name: string
  displayName: string
  deptId: string
  deptName: string
  isAdmin: boolean
  avatar: string
  perms: string[]
  properties?: Record<string, string>
}

export const useUserStore = defineStore('user', () => {
  const tokenStorage = useToken()
  const userValue = ref<User>()
  const perms = computed(() => userValue.value?.perms)
  const isAdmin = computed(() => userValue.value?.isAdmin)
  const user = computed(() => userValue.value)
  const hasLogin = computed(() => userValue.value !== undefined && userValue.value !== null)

  function getAvatar() {
    if (userValue.value?.avatar) {
      return user.value?.avatar
    }
    else {
      return DefaultAvatar
    }
  }

  async function getUser() {
    if (user.value) {
      return user
    }
    else {
      await fetchUser()
      return user
    }
  }

  async function fetchUser() {
    userValue.value = await authApi.getUserInfo()
  }

  function login(accessToken: string, refreshToken: string, user: User) {
    clear()
    tokenStorage.setAccessToken(accessToken)
    tokenStorage.setRefreshToken(refreshToken)
    userValue.value = user
  }

  async function logout() {
    try {
      // todo request logout api
    }
    finally {
      clear()
    }
  }

  function clear() {
    userValue.value = undefined
    tokenStorage.clear()
  }
  return {
    login,
    logout,
    clear,
    getUser,
    fetchUser,
    getAvatar,
    user,
    perms,
    isAdmin,
    hasLogin,
  }
})
