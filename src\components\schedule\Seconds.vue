<script setup lang="ts">
const props = defineProps<{
  seconds: number | undefined
}>()

const formattedStr = formatSeconds(props.seconds)

function formatSeconds(totalSeconds: number | undefined): string {
  if (totalSeconds === undefined) {
    return ''
  }
  if (totalSeconds < 0) {
    throw new Error('Input cannot be negative.')
  }
  if (totalSeconds === 0) {
    return '0s'
  }

  const days = Math.floor(totalSeconds / (24 * 3600))
  totalSeconds %= (24 * 3600)

  const hours = Math.floor(totalSeconds / 3600)
  totalSeconds %= 3600

  const minutes = Math.floor(totalSeconds / 60)
  const seconds = totalSeconds % 60

  let result = ''
  if (days > 0) {
    result += `${days}d`
  }
  if (hours > 0) {
    result += `${hours}h`
  }
  if (minutes > 0) {
    result += `${minutes}m`
  }
  if (seconds > 0) {
    result += `${seconds}s`
  }

  return result
}
</script>

<template>
  <div>
    {{ formattedStr }}
  </div>
</template>
