<script setup lang="ts">
import { toTypedSchema } from '@vee-validate/zod'
import { userApi } from '~/api/common/user'

const loading = ref(false)
const open = defineModel<boolean>('open')
const schema = toTypedSchema(z.object({
  oldPassword: z.string(),
  newPassword: zext.password(),
  confirmPassword: z.string(),
}).refine(data => data.newPassword === data.confirmPassword, {
  message: '两次输入的密码不一致', // 校验失败时的错误信息
  path: ['confirmPassword'], // 错误信息关联到的字段（可选，但推荐）
}))

const { resetForm, handleSubmit } = useForm({
  validationSchema: schema,
})

const save = handleSubmit(async (values) => {
  try {
    loading.value = true
    await userApi.updatePassword(values)
    success('修改成功')

    open.value = false
    resetForm()
  }
  finally {
    loading.value = false
  }
})

onBeforeMount(() => {
  resetForm()
})
</script>

<template>
  <Panel header="修改密码">
    <form @submit="save">
      <div class="flex flex-col items-center p-4">
        <LPassword name="oldPassword" label="旧密码" />
        <LPassword name="newPassword" label="新密码" />
        <LPassword name="confirmPassword" label="重复输入新密码" />
        <Button class="mt-8" type="submit" :loading="loading" icon="pi pi-save" label="提交" />
      </div>
    </form>
  </Panel>
</template>
