<script setup lang="ts">
import type { Ct } from '~/api/ct/types'
import type { LinePeriodWorkDay, Remain, ScheduleItem, ScheduleMeta } from '~/api/schedule/type'
import { FilterMatchMode } from '@primevue/core/api'

import { Panel } from 'primevue'
import { ctApi } from '~/api/ct'
import { scheduleApi } from '~/api/schedule'
import { PeriodType, WorkSection } from '~/utils/time'

const props = defineProps<{
  uploadDate: string
}>()
const workSection = ref<WorkSection>(WorkSection.SMT)
const workSectionOptions = [WorkSection.SMT, WorkSection.DIP]
const periodType = ref<PeriodType>(PeriodType.MONTHLY)
const periodTypeOptions = [
  { key: PeriodType.MONTHLY, value: '按月' },
  { key: PeriodType.WEEKLY, value: '按周' },
]
provide<WorkSection>('workSection', workSection.value)
const remains = ref<Remain[]>([])
const cts = ref<Ct[]>([])
const scheduleItems = ref<ScheduleItem[]>([])
const linePeriodWorkDays = ref<LinePeriodWorkDay[]>([])
const scheduleMeta = ref<ScheduleMeta>()
const selectedRemain = ref<Remain>()
const showAssignDialog = ref<boolean>(false)
const loading = ref<boolean>(true)
const periods = ref<string[]>([])
const models = ref<string[]>([])
function onClickAssign(data: Remain) {
  selectedRemain.value = data
  showAssignDialog.value = true
}

const periodOptions = ref<string[]>([])

const selectedPeriod = ref<string>(periodOptions.value[0])
const periodFilteredLine = computed(() => linePeriodWorkDays.value.filter(o => o.period === selectedPeriod.value)
  .sort((x, y) => x.line.localeCompare(y.line)))

function onRemoveScheduleItem(item: ScheduleItem) {
  scheduleItems.value = scheduleItems.value.filter(o =>
    !(o.line === item.line
      && o.productionPeriod === item.productionPeriod
      && o.orderPeriod === item.orderPeriod
      && o.partNumber === item.partNumber))
  const idx = remains.value.findIndex(o =>
    o.partNumber === item.partNumber
    && o.period === item.orderPeriod,
  )
  if (idx > -1) {
    remains.value[idx].remain += item.quantity
  }
  else {
    remains.value.push({
      partNumber: item.partNumber,
      model: item.model,
      remain: item.quantity,
      product: item.productName,
      period: item.orderPeriod,
    })
  }
}

function onConfirmAssign(item: ScheduleItem, remain: Remain) {
  const idx = scheduleItems.value.findIndex(o => o.partNumber === item.partNumber
    && o.orderPeriod === item.orderPeriod
    && o.productionPeriod === item.productionPeriod
    && o.line === item.line,
  )
  if (idx > -1) {
    scheduleItems.value[idx].quantity += item.quantity
    scheduleItems.value[idx].costTime = plus(scheduleItems.value[idx].costTime, item.costTime)
  }
  else {
    scheduleItems.value.push(item)
  }

  if (remain.remain > 0) {
    const idx2 = remains.value.findIndex(o =>
      o.partNumber === remain.partNumber
      && o.period === remain.period,
    )
    if (idx2 > -1) {
      remains.value[idx2].remain = remain.remain
    }
  }
  else {
    remains.value = remains.value.filter(o => o.partNumber !== remain.partNumber || o.period !== remain.period)
  }
  showAssignDialog.value = false
}

function onModifyLinePeriodWorkDay(line: LinePeriodWorkDay) {
  const idx = linePeriodWorkDays.value.findIndex(o => o.line === line.line && o.period === line.period)
  if (idx > -1) {
    linePeriodWorkDays.value[idx].periodWorkDays = line.periodWorkDays
  }
}

async function onClickSave() {
  if (scheduleMeta.value) {
    const data = { ...scheduleMeta.value, detail: scheduleItems.value, remains: remains.value, linePeriodWorkDays: linePeriodWorkDays.value }
    data.status = false
    try {
      await scheduleApi.save(data)
      success('暂存成功')
    }
    finally {
      loading.value = false
    }
  }
}

async function onClickRelease() {
  if (scheduleMeta.value) {
    const data = { ...scheduleMeta.value, detail: scheduleItems.value, remains: remains.value, linePeriodWorkDays: linePeriodWorkDays.value }
    data.status = true
    try {
      await scheduleApi.save(data)
      success('发布成功')
    }
    finally {
      loading.value = false
    }
  }
}

watchEffect(() => {
  init()
})

async function init() {
  loading.value = true
  remains.value = []
  scheduleItems.value = []
  linePeriodWorkDays.value = []
  scheduleMeta.value = undefined
  cts.value = []
  const res = await scheduleApi.get(workSection.value, props.uploadDate, periodType.value)
  remains.value = res.remains
  scheduleItems.value = res.detail
  linePeriodWorkDays.value = res.linePeriodWorkDays
  periodOptions.value = linePeriodWorkDays.value.map(o => o.period).sort((x, y) => x > y ? 1 : -1).filter((value, index, self) => {
  // indexOf() 返回数组中第一次出现指定元素的索引
  // 如果当前元素的索引与它第一次出现的索引相同，则表示它是唯一的
    return self.indexOf(value) === index
  },
  )
  selectedPeriod.value = periodOptions.value[0] || ''
  scheduleMeta.value = {
    fileId: res.fileId,
    uploadDate: res.uploadDate,
    status: res.status,
    version: res.version,
    workSection: res.workSection,
    timePeriod: res.timePeriod,
  }
  models.value = Array.from(new Set(scheduleItems.value.map(o => o.model)))
  periods.value = Array.from(new Set(scheduleItems.value.map(o => o.orderPeriod)))

  const res2 = await ctApi.get(workSection.value, props.uploadDate)
  cts.value = res2
  loading.value = false
}

const filters = ref({
  partNumber: { value: null, matchMode: FilterMatchMode.CONTAINS },
  period: { value: null, matchMode: FilterMatchMode.EQUALS },
  model: { value: null, matchMode: FilterMatchMode.EQUALS },
})
</script>

<template>
  <Panel header="计划排产">
    <div class="mb-4 flex gap-4">
      <SelectButton v-model="periodType" :options="periodTypeOptions" option-label="value" option-value="key" :allow-empty="false" />
      <SelectButton v-model="workSection" :options="workSectionOptions" :allow-empty="false" />
    </div>
    <div class="grid grid-cols-24 gap-4">
      <Panel header="待排产品" class="grid-col-span-12">
        <DataTable v-model:filters="filters" :value="remains" paginator data-key="id" :rows="10" filter-display="row">
          <div class="flex items-center justify-between gap-4">
            <IconField>
              <InputIcon>
                <i class="pi pi-search" />
              </InputIcon>
              <InputText v-model="filters.partNumber.value" placeholder="查找产品号" style="width: 10rem;" />
            </IconField>
            <Select v-model="filters.period.value" :options="periods" placeholder="选择周期" :show-clear="true" />
            <Select v-model="filters.model.value" :options="models" placeholder="选择平台" :show-clear="true" />
          </div>
          <Column header="产品号" field="partNumber" />
          <Column header="周期" field="period">
            <template #body="{ data }">
              <Tag :value="data.period" :severity="data.period" />
            </template>
          </Column>
          <Column header="产品名" field="product" />
          <Column header="平台" field="model">
            <template #body="{ data }">
              <Tag :value="data.model" :severity="data.model" />
            </template>
          </Column>
          <Column header="待排数量" field="remain">
            <template #body="{ data }">
              <Tag :value="data.remain" :severity="data.remain" />
            </template>
          </Column>
          <Column header="操作">
            <template #body="slotProps">
              <Button
                label="分配"
                class="p-button-text"
                @click="onClickAssign(slotProps.data)"
              />
            </template>
          </Column>
        </DataTable>
      </Panel>
      <Panel header="产线负载" class="grid-col-span-12">
        <div class="mb-4 p-4">
          <Select v-model="selectedPeriod" :options="periodOptions" />
        </div>
        <LineLoad
          :line-period-work-days="periodFilteredLine"
          :schedule-item="scheduleItems"
          @remove-schedule-item="onRemoveScheduleItem"
          @modify-line-period-work-day="onModifyLinePeriodWorkDay"
        />
      </Panel>
      <div class="fixed bottom-4 right-20">
        <Button label="暂存" :loading="loading" size="large" icon="pi pi-save" rounded severity="secondary" class="mr-4" @click="onClickSave" />
        <Button label="发布" :loading="loading" size="large" icon="pi pi-check" rounded @click="onClickRelease" />
      </div>
      <AssignDialog
        v-model:show="showAssignDialog"
        :cts="cts"
        :line-period-work-days="linePeriodWorkDays"
        :remain="selectedRemain"
        :schedule-items="scheduleItems"

        @remove-schedule-item="onRemoveScheduleItem"
        @confirm="onConfirmAssign"
      />
    </div>
  </Panel>
</template>
