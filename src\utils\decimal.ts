import Decimal from 'decimal.js'

export function plus(a: Decimal.Value, b: Decimal.Value) {
  return new Decimal(a).plus(b).toString()
}

export function mul(a: Decimal.Value, b: Decimal.Value) {
  return Decimal.mul(a, b).toString()
}

export function div(a: Decimal.Value, b: Decimal.Value) {
  return Decimal.div(a, b)
}

export function lte(a: Decimal.Value, b: Decimal.Value) {
  return new Decimal(a).lte(b)
}

export function lt(a: Decimal.Value, b: Decimal.Value) {
  return new Decimal(a).lt(b)
}

export function minus(a: Decimal.Value, b: Decimal.Value) {
  return new Decimal(a).minus(b)
}
